# AWS Resource Inventory Django Application - Execution Plan

## Project Overview
Create a comprehensive Django web application for AWS resource inventory management with support for multiple AWS accounts and business units.

## Phase 1: Project Setup and Environment

### 1.1 Initialize Django Project
- [ ] Create virtual environment
- [ ] Install Django and required dependencies
- [ ] Initialize Django project structure
- [ ] Configure settings for development

### 1.2 Dependencies Installation
```bash
pip install django boto3 django-bootstrap4 python-decouple django-saml2 djangosaml2 python3-saml
```

### 1.3 Project Structure
```
cloud_central_SS/
├── manage.py
├── requirements.txt
├── cloud_inventory/
│   ├── __init__.py
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── inventory/
│   ├── __init__.py
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── admin.py
│   ├── forms.py
│   └── migrations/
├── aws_session/
│   ├── __init__.py
│   ├── models.py
│   ├── session_manager.py
│   └── utils.py
├── authentication/
│   ├── __init__.py
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── decorators.py
│   ├── permissions.py
│   └── saml_settings.py
├── static/
│   ├── css/
│   ├── js/
│   └── images/
└── templates/
    ├── base.html
    ├── authentication/
    └── inventory/
```

## Phase 2: Database Models Design

### 2.1 Core Models
- [ ] **BusinessUnit Model**: Store BU information (HQ, GP, REN, PC)
- [ ] **AWSAccount Model**: Account details with BU relationship
- [ ] **AWSSession Model**: Session configuration and credentials
- [ ] **Resource Models**: Base and specific resource models

### 2.1.1 User Authentication Models
- [ ] **UserProfile Model**: Extended user information with group assignments
- [ ] **UserGroup Model**: Three user groups with permission levels
  - Admin_Group: Full system access
  - Operation_Support_Group: Limited fine-grained access
  - Read_Only_Group: Read-only access
- [ ] **Permission Model**: Fine-grained permissions for operations
- [ ] **SAMLConfiguration Model**: SAML SSO settings and metadata

### 2.2 Resource Models Structure
- [ ] **BaseResource**: Common fields for all AWS resources
- [ ] **EC2Instance**: EC2-specific metadata
- [ ] **S3Bucket**: S3-specific metadata
- [ ] **EKSCluster**: EKS-specific metadata
- [ ] **ECSCluster**: ECS-specific metadata
- [ ] **ECRRepository**: ECR-specific metadata
- [ ] **LambdaFunction**: Lambda-specific metadata

### 2.3 Supporting Models
- [ ] **Tag**: Key-value pairs for resource tags
- [ ] **SecurityGroup**: Security group information
- [ ] **VPC**: VPC details
- [ ] **Subnet**: Subnet information
- [ ] **SSMAgent**: SSM agent status and version info

## Phase 3: User Authentication and Authorization

### 3.1 SAML SSO Integration
- [ ] Configure django-saml2 for SSO authentication
- [ ] Setup SAML identity provider integration
- [ ] Create SAML attribute mapping for user groups
- [ ] Implement SAML metadata exchange
- [ ] Configure SAML certificates and security settings

### 3.2 User Group Management
- [ ] **Admin_Group Implementation**:
  - Full CRUD operations on all resources
  - User management capabilities
  - System configuration access
  - AWS session management
  - Data collection and refresh operations
  - Export and reporting features

- [ ] **Operation_Support_Group Implementation**:
  - Read access to all resources
  - Limited write access to specific operations
  - Resource tagging and annotation
  - Basic reporting capabilities
  - No user management access
  - No system configuration changes

- [ ] **Read_Only_Group Implementation**:
  - View-only access to resources
  - Basic filtering and search
  - Export filtered results
  - No modification capabilities
  - No administrative functions

### 3.3 Permission System
- [ ] Create custom permission decorators
- [ ] Implement view-level permission checks
- [ ] Create template-level permission filtering
- [ ] Implement API endpoint authorization
- [ ] Create audit logging for user actions

### 3.4 Authentication Fallback
- [ ] Local Django authentication as fallback
- [ ] Admin user creation for initial setup
- [ ] Password reset functionality
- [ ] Session management and timeout

## Phase 4: AWS Session Management

### 4.1 Session Manager Implementation
- [ ] Create session manager class with three authentication methods:
  - Profile-based authentication
  - Access key/secret key authentication
  - STS assume role/instance profile authentication
- [ ] Implement session validation and error handling
- [ ] Create session factory pattern for different auth methods

### 4.2 Account Management
- [ ] CSV import functionality for AWS accounts
- [ ] Account validation and region support
- [ ] Business unit mapping and relationships

## Phase 5: AWS Resource Collection

### 5.1 Resource Collectors
- [ ] **EC2Collector**: Collect instances with metadata
  - Operating system details
  - Platform information (Windows/Linux)
  - vCPU count and memory size
  - Security groups and network information
  - SSM agent status and version
  - All available tags
- [ ] **S3Collector**: Collect bucket information
- [ ] **EKSCollector**: Collect cluster details
- [ ] **ECSCollector**: Collect cluster and service info
- [ ] **ECRCollector**: Collect repository details
- [ ] **LambdaCollector**: Collect function metadata

### 5.2 Data Processing
- [ ] Implement data normalization and validation
- [ ] Create bulk insert operations for performance
- [ ] Handle API rate limiting and pagination
- [ ] Error handling and retry mechanisms

## Phase 6: Web Interface Development

### 6.1 Frontend Framework Setup
- [ ] Integrate Bootstrap 4 for responsive design
- [ ] Setup jQuery for AJAX functionality
- [ ] Create base template with navigation and user authentication
- [ ] Implement role-based navigation menus
- [ ] Create login/logout templates

### 6.2 Core Views and Templates
- [ ] **Dashboard**: Overview of all resources (role-based content)
- [ ] **Resource List**: Paginated list with filtering (permission-aware)
- [ ] **Resource Detail**: Comprehensive resource information
- [ ] **Search Interface**: Advanced search and filtering
- [ ] **Account Management**: Manage AWS accounts and sessions (Admin only)
- [ ] **User Management**: User and group administration (Admin only)
- [ ] **Login/SSO Pages**: Authentication interface

### 6.3 Role-Based UI Components
- [ ] Admin-only buttons and forms
- [ ] Operation Support limited actions
- [ ] Read-only user interface restrictions
- [ ] Permission-based template rendering

### 6.4 Filtering and Search Features
- [ ] Filter by resource type
- [ ] Filter by business unit
- [ ] Filter by AWS account
- [ ] Filter by tags (key-value pairs)
- [ ] Filter by operating system
- [ ] Filter by region
- [ ] Advanced search with multiple criteria

## Phase 7: API and AJAX Implementation

### 7.1 REST API Endpoints
- [ ] Resource listing API with pagination (permission-aware)
- [ ] Resource detail API
- [ ] Search and filter API
- [ ] Account management API (Admin only)
- [ ] User management API (Admin only)
- [ ] Authentication status API

### 7.2 AJAX Integration
- [ ] Dynamic filtering without page reload
- [ ] Real-time search suggestions
- [ ] Asynchronous resource updates
- [ ] Progress indicators for long operations

## Phase 8: Data Management Features

### 8.1 Data Collection Automation
- [ ] Scheduled data collection tasks (Admin only)
- [ ] Manual refresh functionality (Admin/Operation Support)
- [ ] Incremental updates
- [ ] Data freshness indicators

### 8.2 Data Export and Reporting
- [ ] Export filtered results to CSV
- [ ] Generate summary reports
- [ ] Resource utilization analytics

## Phase 9: Security and Performance

### 9.1 Security Implementation
- [ ] Secure credential storage
- [ ] Input validation and sanitization
- [ ] CSRF protection
- [ ] SQL injection prevention
- [ ] SAML security best practices
- [ ] Session security and timeout
- [ ] Audit logging for sensitive operations
- [ ] Role-based access control enforcement

### 9.2 Performance Optimization
- [ ] Database indexing strategy
- [ ] Query optimization
- [ ] Caching implementation
- [ ] Pagination for large datasets

## Phase 10: Testing and Documentation

### 10.1 Testing Strategy
- [ ] Unit tests for models and utilities
- [ ] Integration tests for AWS collectors
- [ ] Authentication and authorization tests
- [ ] SAML SSO integration tests
- [ ] Permission-based access tests
- [ ] Frontend testing for user interactions
- [ ] Performance testing with large datasets

### 10.2 Documentation
- [ ] API documentation
- [ ] User manual
- [ ] Deployment guide
- [ ] Configuration documentation

## Phase 11: Deployment Preparation

### 11.1 Production Configuration
- [ ] Environment-specific settings
- [ ] SAML production configuration
- [ ] Database migration scripts
- [ ] Static file collection
- [ ] Error logging configuration
- [ ] Security headers and SSL configuration

### 11.2 Deployment Scripts
- [ ] Requirements.txt finalization
- [ ] Database initialization scripts
- [ ] Sample data loading scripts

## Technical Specifications

### Database Schema Relationships
- BusinessUnit (1) → (N) AWSAccount
- AWSAccount (1) → (N) Resources
- Resource (1) → (N) Tags
- Resource (N) → (N) SecurityGroups
- VPC (1) → (N) Subnets
- EC2Instance (1) → (1) SSMAgent
- User (1) → (1) UserProfile
- UserGroup (1) → (N) UserProfile
- UserGroup (1) → (N) Permission

### User Group Permissions Matrix
| Feature | Admin_Group | Operation_Support_Group | Read_Only_Group |
|---------|-------------|-------------------------|-----------------|
| View Resources | ✅ | ✅ | ✅ |
| Filter/Search | ✅ | ✅ | ✅ |
| Export Data | ✅ | ✅ | ✅ |
| Add/Edit Resources | ✅ | ❌ | ❌ |
| Delete Resources | ✅ | ❌ | ❌ |
| Manage AWS Sessions | ✅ | ❌ | ❌ |
| User Management | ✅ | ❌ | ❌ |
| System Configuration | ✅ | ❌ | ❌ |
| Data Collection | ✅ | Limited | ❌ |
| Resource Tagging | ✅ | ✅ | ❌ |

### Key Features Implementation Priority
1. **High Priority**: Authentication/SSO, user groups, resource listing, basic filtering, AWS session management
2. **Medium Priority**: Advanced search, detailed views, data export, permission enforcement
3. **Low Priority**: Analytics, automated collection, advanced reporting

### Performance Considerations
- Use database indexes on frequently queried fields
- Implement pagination for large result sets
- Use AJAX for dynamic content updates
- Cache frequently accessed data
- Optimize AWS API calls with batch operations

## Success Criteria
- [ ] Successfully collect and display all specified AWS resources
- [ ] Support multiple authentication methods (local + SAML SSO)
- [ ] Implement three user groups with proper permission enforcement
- [ ] Provide comprehensive filtering and search capabilities
- [ ] Maintain responsive design across devices
- [ ] Handle multiple AWS accounts and business units
- [ ] Ensure data accuracy and freshness
- [ ] Secure authentication and authorization system
- [ ] Role-based access control throughout the application
- [ ] Audit logging for administrative actions

## Additional Security Considerations
- [ ] SAML certificate management and rotation
- [ ] Session timeout and security policies
- [ ] Input validation and XSS prevention
- [ ] Secure storage of AWS credentials
- [ ] Rate limiting for API endpoints
- [ ] Comprehensive audit trails
- [ ] Data encryption at rest and in transit
