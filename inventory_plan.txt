create an django web application with below features,
1. aws resource inventory for below resources
    a. EC2 instances 
    b. S3 buckets
    c. EKS Clusters
    d. ECS Clusters
    e. ECR Repositories
    f. Lambda Functions
2. resources should have all its metadata like and which ever applicabile below list:
    a. operating system(platform details like windows or linux) and detailed os info like os version & kernel version
    b. ssm agent (status,current version, is_latest, latest version)
    c. all tags (in an queriable format like key value pair)
    d. all available resource information
    e. all available security information like security groups, network acl, vpc, subnets, etc.
    f. vcpu count and memory size




all the data collected should be stored in data base and should be queriable
data schema:
   all data should have proper relationships and can be queried and filtered based on them

the inventory app should have below features:
   1. list all resources
   2. filter resources based on metadata
   3. search resources based on metadata
   4. view resource details
  
use aws boto3 to collect all the data
and use below code snippet to get aws login session:
      
session = boto3.session.Session(profile_name=f"support-{account_id}", region_name=region)

create a session manager and let it have below features:
aws sessions can be obtained by below methods
  1. using profile name (above code snippet)
  2. using access key and secret key
  3. using assume role (sts) or instance_profile

and the account lists will be provided in a csv file with below format:
Account Id,Account Name,Business Unit

have a related model for BU (Business Unit):
Vernova CTO HQ : HQ
Gas Power : GP
Renewables : REN
Power Conversions: PC

example: 
************,us-east-1,GP
************,us-east-1,REN
************,us-east-1,HQ
************,eu-west-1,PC


Techstack to use:
 1. django
 2. python
 3. boto3
 4. sqlite
 5. html, css, js
 6. bootstrap
 7. jquery
 8. ajax
 9. responsive design